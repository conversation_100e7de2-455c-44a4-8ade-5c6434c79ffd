package com.chervon.usercenter.application.impl;

import com.chervon.common.core.prop.SfProperties;
import com.chervon.common.core.utils.AesUtils;
import com.chervon.common.core.utils.CommonUtil;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.usercenter.api.dto.*;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.exception.UserCenterException;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.sf.SfUserRecord;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import com.chervon.usercenter.domain.factory.UserFactory;
import com.chervon.usercenter.domain.model.user.Email;
import com.chervon.usercenter.domain.model.user.Password;
import com.chervon.usercenter.domain.model.user.User;
import com.chervon.usercenter.domain.model.user.UserRepository;
import com.chervon.usercenter.domain.model.user.UserStatusEnum;
import com.chervon.usercenter.infrastructure.converter.UserConverter;
import com.chervon.usercenter.infrastructure.entity.UserDo;
import com.chervon.usercenter.infrastructure.mq.SfUserMessage;
import com.chervon.usercenter.infrastructure.mq.StreamProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UserCommandServiceImpl 单元测试
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
class UserCommandServiceImplTest {

    @Mock
    private UserQueryService userQueryService;

    @Mock
    private UserRepository userRepository;

    @Mock
    private StreamProducer streamProducer;

    @Mock
    private SaleForceService saleForceService;

    @Mock
    private SfProperties sfProperties;

    @InjectMocks
    private UserCommandServiceImpl userCommandService;

    private UserRegisterDto userRegisterDto;
    private ConfirmPasswordDto confirmPasswordDto;
    private EditPasswordDto editPasswordDto;
    private ResetPasswordDto resetPasswordDto;
    private PhoneInfoDto phoneInfoDto;
    private UserEditDto userEditDto;
    private User mockUser;
    private UserDo mockUserDo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        userRegisterDto = new UserRegisterDto();
        userRegisterDto.setEmail("<EMAIL>");
        userRegisterDto.setFirstName("John");
        userRegisterDto.setLastName("Doe");
        userRegisterDto.setPassword("encryptedPassword");
        userRegisterDto.setCode("123456");
        userRegisterDto.setCountry("US");
        userRegisterDto.setCity("New York");
        userRegisterDto.setPostcode("10001");
        userRegisterDto.setPhone("1234567890");

        confirmPasswordDto = new ConfirmPasswordDto();
        confirmPasswordDto.setEmail("<EMAIL>");
        confirmPasswordDto.setPassword("encryptedPassword");

        editPasswordDto = new EditPasswordDto();
        editPasswordDto.setEmail("<EMAIL>");
        editPasswordDto.setPassword("newEncryptedPassword");
        editPasswordDto.setOldPassword("oldEncryptedPassword");

        resetPasswordDto = new ResetPasswordDto();
        resetPasswordDto.setEmail("<EMAIL>");
        resetPasswordDto.setPassword("newEncryptedPassword");
        resetPasswordDto.setCode("123456");

        phoneInfoDto = new PhoneInfoDto();
        phoneInfoDto.setPhoneOsVersion("iOS 15.0");
        phoneInfoDto.setPhoneModel("iPhone 13");
        phoneInfoDto.setAppVersion("1.0.0");
        phoneInfoDto.setIp("***********");
        phoneInfoDto.setAppTypeCode("ios");

        userEditDto = new UserEditDto();
        userEditDto.setId(1L);
        userEditDto.setFirstName("Jane");
        userEditDto.setLastName("Smith");
        userEditDto.setPhoto("photo.jpg");

        mockUser = new User();
        mockUser.setUserId(1L);
        mockUser.setEmail(new Email("<EMAIL>"));

        // 创建mock密码对象
        Password mockPassword = new Password("hashedPassword", "salt", "aesPassword", "aesSalt");
        mockUser.setPassword(mockPassword);

        mockUserDo = new UserDo();
        mockUserDo.setId(1L);
        mockUserDo.setEmail("<EMAIL>");
        mockUserDo.setPassword("hashedPassword");
        mockUserDo.setSalt("salt");
        mockUserDo.setAesPassword("aesPassword");
        mockUserDo.setAesSalt("aesSalt");
    }

    /**
     * 测试用户注册 - 成功场景
     */
    @Test
    void testRegister_Success() {
        // Given
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class);
             MockedStatic<ExceptionMessageUtil> exceptionUtilMock = mockStatic(ExceptionMessageUtil.class)) {

            // Mock Redis验证码校验
            redisUtilsMock.when(() -> RedisUtils.getCacheMapValue(anyString(), eq(UserCenterConstant.CODE)))
                    .thenReturn("123456");

            // Mock 邮箱未被使用
            when(userQueryService.checkEmailUsed(anyString())).thenReturn(false);

            // Mock SF服务未启用
            when(sfProperties.isEnable()).thenReturn(false);

            // Mock UserFactory和User创建
            try (MockedStatic<UserFactory> userFactoryMock = mockStatic(UserFactory.class)) {
                UserFactory mockUserFactory = mock(UserFactory.class);
                userFactoryMock.when(() -> new UserFactory(any(UserRepository.class)))
                        .thenReturn(mockUserFactory);
                when(mockUserFactory.createUser(any(UserRegisterDto.class), any(UserStatusEnum.class)))
                        .thenReturn(mockUser);

                // When
                boolean result = userCommandService.register(userRegisterDto);

                // Then
                assertTrue(result);
                verify(userRepository).store(any(User.class));
                verify(streamProducer).streamSfUserMsg(eq(SfUserMessage.TypeEnum.ADD), any(User.class));
            }
        }
    }

    /**
     * 测试用户注册 - LastName缺失
     */
    @Test
    void testRegister_MissingLastName() {
        // Given
        userRegisterDto.setLastName("");

        try (MockedStatic<ExceptionMessageUtil> exceptionUtilMock = mockStatic(ExceptionMessageUtil.class)) {
            UserCenterException expectedException = new UserCenterException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_ADD_USER_ERROR);
            exceptionUtilMock.when(() -> ExceptionMessageUtil.getException(
                    eq(UserCenterErrorCode.USER_CENTER_SALE_FORCE_ADD_USER_ERROR), anyString()))
                    .thenReturn(expectedException);

            // When & Then
            assertThrows(UserCenterException.class, () -> userCommandService.register(userRegisterDto));
        }
    }

    /**
     * 测试用户注册 - 验证码错误
     */
    @Test
    void testRegister_InvalidCode() {
        // Given
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class);
             MockedStatic<ExceptionMessageUtil> exceptionUtilMock = mockStatic(ExceptionMessageUtil.class)) {

            // Mock Redis验证码校验失败
            redisUtilsMock.when(() -> RedisUtils.getCacheMapValue(anyString(), eq(UserCenterConstant.CODE)))
                    .thenReturn("654321"); // 不同的验证码

            UserCenterException expectedException = new UserCenterException(UserCenterErrorCode.USER_CENTER_EMAIL_CODE_ERROR1);
            exceptionUtilMock.when(() -> ExceptionMessageUtil.getException(eq(UserCenterErrorCode.USER_CENTER_EMAIL_CODE_ERROR1)))
                    .thenReturn(expectedException);

            // When & Then
            assertThrows(UserCenterException.class, () -> userCommandService.register(userRegisterDto));
        }
    }

    /**
     * 测试用户注册 - 邮箱已被使用
     */
    @Test
    void testRegister_EmailAlreadyUsed() {
        // Given
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class);
             MockedStatic<ExceptionMessageUtil> exceptionUtilMock = mockStatic(ExceptionMessageUtil.class)) {

            // Mock Redis验证码校验通过
            redisUtilsMock.when(() -> RedisUtils.getCacheMapValue(anyString(), eq(UserCenterConstant.CODE)))
                    .thenReturn("123456");

            // Mock 邮箱已被使用
            when(userQueryService.checkEmailUsed(anyString())).thenReturn(true);

            UserCenterException expectedException = new UserCenterException(UserCenterErrorCode.USER_CENTER_EMAIL_USED);
            exceptionUtilMock.when(() -> ExceptionMessageUtil.getException(
                    eq(UserCenterErrorCode.USER_CENTER_EMAIL_USED), anyString()))
                    .thenReturn(expectedException);

            // When & Then
            assertThrows(UserCenterException.class, () -> userCommandService.register(userRegisterDto));
        }
    }

    /**
     * 测试确认密码 - 成功场景
     */
    @Test
    void testConfirmPassword_Success() {
        // Given
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class);
             MockedStatic<AesUtils> aesUtilsMock = mockStatic(AesUtils.class)) {

            when(userRepository.find(any(Email.class))).thenReturn(mockUser);
            redisUtilsMock.when(() -> RedisUtils.getCacheObject(anyString())).thenReturn("aesKey");
            aesUtilsMock.when(() -> AesUtils.decrypt(anyString(), anyString())).thenReturn("plainPassword");
            when(mockUser.validPassword(anyString(), any(Password.class))).thenReturn(true);

            // When
            boolean result = userCommandService.confirmPassword(confirmPasswordDto);

            // Then
            assertTrue(result);
        }
    }

    /**
     * 测试确认密码 - 用户不存在
     */
    @Test
    void testConfirmPassword_UserNotFound() {
        // Given
        try (MockedStatic<ExceptionMessageUtil> exceptionUtilMock = mockStatic(ExceptionMessageUtil.class)) {
            when(userRepository.find(any(Email.class))).thenReturn(null);

            UserCenterException expectedException = new UserCenterException(UserCenterErrorCode.USER_CENTER_USER_NOT_FOUND);
            exceptionUtilMock.when(() -> ExceptionMessageUtil.getException(
                    eq(UserCenterErrorCode.USER_CENTER_USER_NOT_FOUND), anyString()))
                    .thenReturn(expectedException);

            // When & Then
            assertThrows(UserCenterException.class, () -> userCommandService.confirmPassword(confirmPasswordDto));
        }
    }

    /**
     * 测试修改密码 - 成功场景
     */
    @Test
    void testEditPassword_Success() {
        // Given
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class);
             MockedStatic<AesUtils> aesUtilsMock = mockStatic(AesUtils.class)) {

            when(userRepository.find(any(Email.class))).thenReturn(mockUser);
            redisUtilsMock.when(() -> RedisUtils.getCacheObject(anyString())).thenReturn("aesKey");
            aesUtilsMock.when(() -> AesUtils.decrypt(eq("oldEncryptedPassword"), anyString())).thenReturn("oldPassword");
            aesUtilsMock.when(() -> AesUtils.decrypt(eq("newEncryptedPassword"), anyString())).thenReturn("newPassword");
            when(mockUser.validPassword(eq("oldPassword"), any(Password.class))).thenReturn(true);

            // When
            boolean result = userCommandService.editPassword(editPasswordDto);

            // Then
            assertTrue(result);
            verify(userRepository).store(any(User.class));
            verify(streamProducer).streamSfUserMsg(eq(SfUserMessage.TypeEnum.UPDATE), any(User.class));
        }
    }

    /**
     * 测试修改密码 - 旧密码错误
     */
    @Test
    void testEditPassword_WrongOldPassword() {
        // Given
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class);
             MockedStatic<AesUtils> aesUtilsMock = mockStatic(AesUtils.class);
             MockedStatic<ExceptionMessageUtil> exceptionUtilMock = mockStatic(ExceptionMessageUtil.class)) {

            when(userRepository.find(any(Email.class))).thenReturn(mockUser);
            redisUtilsMock.when(() -> RedisUtils.getCacheObject(anyString())).thenReturn("aesKey");
            aesUtilsMock.when(() -> AesUtils.decrypt(anyString(), anyString())).thenReturn("wrongPassword");
            when(mockUser.validPassword(anyString(), any(Password.class))).thenReturn(false);

            UserCenterException expectedException = new UserCenterException(UserCenterErrorCode.USER_CENTER_OLD_PASSWORD_ERROR);
            exceptionUtilMock.when(() -> ExceptionMessageUtil.getException(eq(UserCenterErrorCode.USER_CENTER_OLD_PASSWORD_ERROR)))
                    .thenReturn(expectedException);

            // When & Then
            assertThrows(UserCenterException.class, () -> userCommandService.editPassword(editPasswordDto));
        }
    }

    /**
     * 测试重置密码 - 成功场景
     */
    @Test
    void testResetPassword_Success() {
        // Given
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class);
             MockedStatic<AesUtils> aesUtilsMock = mockStatic(AesUtils.class)) {

            when(userRepository.find(any(Email.class))).thenReturn(mockUser);
            redisUtilsMock.when(() -> RedisUtils.getCacheMapValue(anyString(), eq(UserCenterConstant.CODE)))
                    .thenReturn("123456");
            redisUtilsMock.when(() -> RedisUtils.getCacheObject(anyString())).thenReturn("aesKey");
            aesUtilsMock.when(() -> AesUtils.decrypt(anyString(), anyString())).thenReturn("newPassword");

            // When
            boolean result = userCommandService.resetPassword(resetPasswordDto);

            // Then
            assertTrue(result);
            verify(userRepository).store(any(User.class));
            verify(streamProducer).streamSfUserMsg(eq(SfUserMessage.TypeEnum.UPDATE), any(User.class));
        }
    }

    /**
     * 测试重置密码 - 验证码错误
     */
    @Test
    void testResetPassword_InvalidCode() {
        // Given
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class);
             MockedStatic<ExceptionMessageUtil> exceptionUtilMock = mockStatic(ExceptionMessageUtil.class)) {

            when(userRepository.find(any(Email.class))).thenReturn(mockUser);
            redisUtilsMock.when(() -> RedisUtils.getCacheMapValue(anyString(), eq(UserCenterConstant.CODE)))
                    .thenReturn("654321"); // 错误的验证码

            UserCenterException expectedException = new UserCenterException(UserCenterErrorCode.USER_CENTER_EMAIL_CODE_ERROR1);
            exceptionUtilMock.when(() -> ExceptionMessageUtil.getException(eq(UserCenterErrorCode.USER_CENTER_EMAIL_CODE_ERROR1)))
                    .thenReturn(expectedException);

            // When & Then
            assertThrows(UserCenterException.class, () -> userCommandService.resetPassword(resetPasswordDto));
        }
    }

    /**
     * 测试注销用户 - 成功场景
     */
    @Test
    void testLogOffUser_Success() {
        // Given
        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            when(userRepository.find(any(Email.class))).thenReturn(mockUser);

            // When
            boolean result = userCommandService.logOffUser("<EMAIL>");

            // Then
            assertTrue(result);
            verify(userRepository).delete(any(Long.class));
            verify(streamProducer).streamSfUserMsg(eq(SfUserMessage.TypeEnum.DELETE), any(User.class));

            // 验证Redis缓存清理
            redisUtilsMock.verify(() -> RedisUtils.deleteObject(anyString()), times(5));
        }
    }

    /**
     * 测试注销用户 - 用户不存在
     */
    @Test
    void testLogOffUser_UserNotFound() {
        // Given
        try (MockedStatic<ExceptionMessageUtil> exceptionUtilMock = mockStatic(ExceptionMessageUtil.class)) {
            when(userRepository.find(any(Email.class))).thenReturn(null);

            UserCenterException expectedException = new UserCenterException(UserCenterErrorCode.USER_CENTER_USER_NOT_FOUND);
            exceptionUtilMock.when(() -> ExceptionMessageUtil.getException(
                    eq(UserCenterErrorCode.USER_CENTER_USER_NOT_FOUND), anyString()))
                    .thenReturn(expectedException);

            // When & Then
            assertThrows(UserCenterException.class, () -> userCommandService.logOffUser("<EMAIL>"));
        }
    }

    /**
     * 测试上报手机信息
     */
    @Test
    void testReportPhoneInfo() {
        // Given
        Long userId = 1L;

        try (MockedStatic<com.chervon.common.core.utils.ConvertUtil> convertUtilMock =
                mockStatic(com.chervon.common.core.utils.ConvertUtil.class)) {

            convertUtilMock.when(() -> com.chervon.common.core.utils.ConvertUtil.convert(any(PhoneInfoDto.class), eq(User.class)))
                    .thenReturn(mockUser);

            // When
            userCommandService.reportPhoneInfo(userId, phoneInfoDto);

            // Then
            verify(userRepository).updateUserPhone(any(User.class));
            assertEquals(userId, mockUser.getUserId());
        }
    }

    /**
     * 测试编辑用户信息 - 成功场景
     */
    @Test
    void testEditUserInfo_Success() {
        // Given
        try (MockedStatic<UserConverter> userConverterMock = mockStatic(UserConverter.class)) {
            userConverterMock.when(() -> UserConverter.userEditDtoToUser(any(UserEditDto.class)))
                    .thenReturn(mockUser);

            // When
            userCommandService.editUserInfo(userEditDto);

            // Then
            verify(userRepository).update(any(UserDo.class), any());
            verify(streamProducer).streamSfUserMsg(eq(SfUserMessage.TypeEnum.UPDATE), any(User.class));
        }
    }

    /**
     * 测试编辑用户信息 - ID为空
     */
    @Test
    void testEditUserInfo_NullId() {
        // Given
        userEditDto.setId(null);

        // When
        userCommandService.editUserInfo(userEditDto);

        // Then
        verify(userRepository, never()).update(any(), any());
        verify(streamProducer, never()).streamSfUserMsg(any(), any());
    }

    /**
     * 测试编辑用户头像 - 成功场景
     */
    @Test
    void testEditUserPhoto_Success() {
        // When
        userCommandService.editUserPhoto(userEditDto);

        // Then
        verify(userRepository).update(any(UserDo.class), any());
    }

    /**
     * 测试编辑用户头像 - ID为空
     */
    @Test
    void testEditUserPhoto_NullId() {
        // Given
        userEditDto.setId(null);

        // When
        userCommandService.editUserPhoto(userEditDto);

        // Then
        verify(userRepository, never()).update(any(), any());
    }

    /**
     * 测试编辑用户在线状态 - 成功场景
     */
    @Test
    void testEditUserPresenceState_Success() {
        // Given
        Long userId = 1L;
        String appPresenceCode = "online";

        // When
        userCommandService.editUserPresenceState(userId, appPresenceCode);

        // Then
        verify(userRepository).updateById(any(UserDo.class));
    }

    /**
     * 测试编辑用户在线状态 - 无效状态
     */
    @Test
    void testEditUserPresenceState_InvalidState() {
        // Given
        Long userId = 1L;
        String appPresenceCode = "invalid";

        // When
        userCommandService.editUserPresenceState(userId, appPresenceCode);

        // Then
        verify(userRepository, never()).updateById(any());
    }

    /**
     * 测试编辑用户在线状态 - 空参数
     */
    @Test
    void testEditUserPresenceState_NullParams() {
        // When
        userCommandService.editUserPresenceState(null, "online");
        userCommandService.editUserPresenceState(1L, null);

        // Then
        verify(userRepository, never()).updateById(any());
    }

    /**
     * 测试压力测试用户注册
     */
    @Test
    void testTestRegisterUser() {
        // Given
        Integer start = 1;
        Integer count = 5;

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            // When
            userCommandService.testRegisterUser(start, count);

            // Then
            verify(userRepository).saveBatch(anyList());
            redisUtilsMock.verify(() -> RedisUtils.setCacheObject(anyString(), anyString(), any()), times(count));
        }
    }

    /**
     * 测试从SF同步用户 - 空列表
     */
    @Test
    void testSyncUserFromSf_EmptyList() {
        // Given
        List<SfUserRecord> emptyList = new ArrayList<>();

        // When
        userCommandService.syncUserFromSf(emptyList);

        // Then
        verify(userRepository, never()).list(any());
        verify(userRepository, never()).saveBatch(any());
        verify(userRepository, never()).updateBatchById(any());
    }

    /**
     * 测试从SF同步用户 - null列表
     */
    @Test
    void testSyncUserFromSf_NullList() {
        // When
        userCommandService.syncUserFromSf(null);

        // Then
        verify(userRepository, never()).list(any());
        verify(userRepository, never()).saveBatch(any());
        verify(userRepository, never()).updateBatchById(any());
    }

    /**
     * 测试从SF同步用户 - 新用户添加
     */
    @Test
    void testSyncUserFromSf_AddNewUser() {
        // Given
        List<SfUserRecord> sfUserRecords = createMockSfUserRecords();
        List<UserDo> existingUsers = new ArrayList<>(); // 空列表，表示没有现有用户

        try (MockedStatic<UserConverter> userConverterMock = mockStatic(UserConverter.class);
             MockedStatic<CommonUtil> commonUtilMock = mockStatic(CommonUtil.class);
             MockedStatic<AesUtils> aesUtilsMock = mockStatic(AesUtils.class)) {

            when(userRepository.list(any())).thenReturn(existingUsers);
            userConverterMock.when(() -> UserConverter.sfUserRecordToUserDo(any(SfUserRecord.class)))
                    .thenReturn(mockUserDo);
            commonUtilMock.when(() -> CommonUtil.getGUID(anyInt())).thenReturn("mockGuid");
            commonUtilMock.when(() -> CommonUtil.encrypt3ToMD5(anyString())).thenReturn("hashedPassword");
            aesUtilsMock.when(() -> AesUtils.encrypt(anyString(), anyString())).thenReturn("encryptedPassword");

            // When
            userCommandService.syncUserFromSf(sfUserRecords);

            // Then
            verify(userRepository).saveBatch(anyList());
            verify(userRepository, never()).updateBatchById(any());
        }
    }

    /**
     * 创建模拟的SF用户记录
     */
    private List<SfUserRecord> createMockSfUserRecords() {
        List<SfUserRecord> records = new ArrayList<>();
        SfUserRecord record = new SfUserRecord();
        record.setSfUserId("SF001");
        record.setUsername("<EMAIL>");
        record.setPassword("password123");
        record.setFirstName("John");
        record.setLastName("Doe");
        records.add(record);
        return records;
    }
}
