package com.chervon.usercenter.infrastructure.repository;

import com.chervon.usercenter.UserCenterApplication;
import com.chervon.usercenter.api.dto.InviteRegisterDto;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = UserCenterApplication.class)
class SendEmailServiceIntegrationTest {

    @Autowired
    private SendEmailService sendEmailService;

    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_CODE = "123456";
    private static final String TEST_LANGUAGE = "us";

    @Test
    void testSendCode_ForRegister() {
        assertDoesNotThrow(() -> 
            sendEmailService.sendCode(TEST_CODE, TEST_EMAIL, TEST_LANGUAGE, UserCenterConstant.REGISTER)
        );
    }

    @Test
    void testSendCode_ForForgetPassword() {
        assertDoesNotThrow(() -> 
            sendEmailService.sendCode(TEST_CODE, TEST_EMAIL, TEST_LANGUAGE, UserCenterConstant.FORGET_PASSWORD)
        );
    }

    @Test
    void testSendInviteRegister() {
        InviteRegisterDto dto = new InviteRegisterDto();
        dto.setEmail(TEST_EMAIL);
        dto.setLanguage(TEST_LANGUAGE);
        dto.setFirstName("John");
        dto.setLastName("Doe");
        dto.setProductName("TestProduct");

        assertDoesNotThrow(() -> sendEmailService.sendInviteRegister(dto));
    }

    @Test
    void testSendEmail() {
        String testContent = "<html><head><title>Test Email</title></head><body>Test Content</body></html>";
        assertDoesNotThrow(() -> sendEmailService.sendEmail(testContent, TEST_EMAIL));
    }
}