package com.chervon.usercenter.api.service;

import com.alibaba.fastjson.JSONObject;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.usercenter.api.dto.sf.SfSurveySubmitDto;
import com.chervon.usercenter.api.dto.sf.SfUserAddDto;
import com.chervon.usercenter.api.dto.sf.SfWarrantyRegisterDto;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @Author：flynn.wang
 * @Date：2024/2/1 20:04
 */

@SpringBootTest
public class SalesForceServiceIntegrationTest {


    @Resource(name = UserCenterConstant.NA)
    private SaleForceService saleForceService;




    /**
     * 测试sf添加问卷调查接口
     */
    @Test
    public void testDeleteSfUser() {

        saleForceService.deleteSfUser("001VE00000JSMFaYAP");
    }


    /**
     * 测试sf添加问卷调查接口
     */
    @Test
    public void testListSfProduct() {
        saleForceService.listSfProduct("RM2000E","United States");
    }


    /**
     * 测试sf添加问卷调查接口
     */
    @Test
    public void testSubmitSurvey() {
        String reqContent = "{\n" +
                "  \"customerId\": \"0018a00001zukvZAAQ1\", \n" +
                "  \"responseList\": [\n" +
                "    {\n" +
                "      \"result\": {\n" +
                "        \"manualText\": \"\", \n" +
                "        \"haveManualText\": false, \n" +
                "        \"type\": \"Single Select\", \n" +
                "        \"answer\": \"a3A0h00000303f7EAA\"\n" +
                "      }, \n" +
                "      \"questionId\": \"a3C0h000000ye6AEAQ\"\n" +
                "    }\n" +
                "  ], \n" +
                "  \"serialNumber\": \"EST27240400404X\"\n" +
                "}";
        SfSurveySubmitDto sfSurveySubmitDto = JsonUtils.parseObject(reqContent, SfSurveySubmitDto.class);
        saleForceService.submitSurvey(sfSurveySubmitDto);
    }

    /**
     * 测试sf注册用户接口
     */
    @Test
    public void testAddSfUser() {
        String json = "{\"description\":\"Iot\",\"eGO_password__c\":\"passwordSource\"," +
                "\"eGO_username__c\":\"<EMAIL>\",\"firstName\":\"Li\"," +
                "\"lastName\":\"yuan\",\"personEmail\":\"<EMAIL>\"," +
                "\"personMobilePhone\":\"1344555674\",\"product_Type__c\":\"EGO\"," +
                "\"recordTypeId\":\"0121A000000CD7lQAG\",\"shippingCity\":\"ningbo\"," +
                "\"shippingCountry\":\"ch\"," +
                "\"shippingPostalCode\":\"112233\",\"shippingStreet\":\"gaoqiao\"}\n";
        SfUserAddDto sfUserAddDto = JSONObject.parseObject(json, SfUserAddDto.class);
        saleForceService.addSfUser(sfUserAddDto);
    }

    /**
     * 测试sf注册质保接口
     */
    @Test
    public void testAddSfWarranty() {
        String json = "{\"records\":[{\"accountCustomer__c\":\"001QI00000GtzMbYAJ\",\"gift__c\":true," +
                "\"lost_Receipt__c\":false,\"master_Product__c\":\"ST150\",\"pending__c\":false," +
                "\"place_of_Purchase__c\":\"Amazon\",\"product_Use_Type2__c\":\"Residential\"," +
                "\"purchase_Date__c\":\"********\"," +
                "\"warranty_Items__r\":{" +
                "\"records\":[" +
                "{\"product_Model__c\":\"BA1400\",\"serial_Number__c\":\"EA110817330\"}]}}]}";
        SfWarrantyRegisterDto warrantyRegisterDto = JSONObject.parseObject(json, SfWarrantyRegisterDto.class);
        saleForceService.registerSfWarranty(warrantyRegisterDto);
    }
}
