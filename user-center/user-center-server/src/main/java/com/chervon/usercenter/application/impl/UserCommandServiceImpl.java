package com.chervon.usercenter.application.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.prop.SfProperties;
import com.chervon.common.core.utils.AesUtils;
import com.chervon.common.core.utils.CommonUtil;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.usercenter.api.dto.*;
import com.chervon.usercenter.api.exception.UserCenterErrorCode;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.service.UserCommandService;
import com.chervon.usercenter.api.service.UserQueryService;
import com.chervon.usercenter.api.vo.sf.SfUserRecord;
import com.chervon.usercenter.config.ExceptionMessageUtil;
import com.chervon.usercenter.domain.constant.UserCenterConstant;
import com.chervon.usercenter.domain.factory.UserFactory;
import com.chervon.usercenter.domain.model.user.Email;
import com.chervon.usercenter.domain.model.user.User;
import com.chervon.usercenter.domain.model.user.UserRepository;
import com.chervon.usercenter.domain.model.user.UserStatusEnum;
import com.chervon.usercenter.infrastructure.converter.UserConverter;
import com.chervon.usercenter.infrastructure.entity.UserDo;
import com.chervon.usercenter.infrastructure.mq.SfUserMessage;
import com.chervon.usercenter.infrastructure.mq.StreamProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-06-14
 */
@DubboService
@Slf4j
@Service
public class UserCommandServiceImpl implements UserCommandService {
    @Autowired
    private UserQueryService userQueryService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private StreamProducer streamProducer;

    @Resource(name = "${sf.direction}")
    private SaleForceService saleForceService;

    @Autowired
    private SfProperties sfProperties;

    @Override
    public boolean register(UserRegisterDto dto) {
        if(!org.springframework.util.StringUtils.hasText(dto.getLastName())) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_SALE_FORCE_ADD_USER_ERROR, "LastName missing");
        }

        String key = dto.getEmail() + UserCenterConstant.REDIST_SPACER + UserCenterConstant.REGISTER;

        // 校验邮箱验证码
        Object hashCode = RedisUtils.getCacheMapValue(key, UserCenterConstant.CODE);
        if (hashCode == null) {
            log.error("Illegal operation ==> redis key:{}", key);
            return false;
        }
        if (!Objects.equals(dto.getCode(), hashCode)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EMAIL_CODE_ERROR1);
        }
        if (userQueryService.checkEmailUsed(dto.getEmail())) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EMAIL_USED, dto.getEmail());
        }
        // 去SF根据邮箱查询用户，如果存在则返回用户已存在，并直接从SF同步该邮箱账号到数据库
        if (sfProperties.isEnable()) {
            SfUserRecord sfUserRecord = saleForceService.getSfUserBySfEmail(dto.getEmail());
            if (null != sfUserRecord) {
                UserDo userDo = UserConverter.sfUserRecordToUserDo(sfUserRecord);
                // MD5加盐加密
                String salt = CommonUtil.getGUID(CommonConstant.EIGHT);
                userDo.setSalt(salt);
                String sourceStr = sfUserRecord.getPassword() + salt;
                String password = CommonUtil.encrypt3ToMD5(sourceStr);
                userDo.setPassword(password);
                // AES密码秘钥
                String aesSalt = CommonUtil.getGUID(CommonConstant.SIXTEEN);
                userDo.setAesSalt(aesSalt);
                userDo.setAesPassword(AesUtils.encrypt(sfUserRecord.getPassword(), aesSalt));
                userDo.setUserTypeCode("old");
                userDo.setUserSourceCode("sf");
                userDo.setIsEmailValidated(CommonConstant.ONE);
                userRepository.save(userDo);
                throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EMAIL_USED, dto.getEmail());
            }
        }
        UserFactory userFactory = new UserFactory(userRepository);
        User user = userFactory.createUser(dto, UserStatusEnum.ACTIVATION);
        // 用户注册过程中邮箱已验证，IsEmailValidated置为1
        user.setIsEmailValidated(CommonConstant.ONE);
        userRepository.store(user);
        // 消息队列 创建SF用户
        streamProducer.streamSfUserMsg(SfUserMessage.TypeEnum.ADD, user);
        return true;
    }

    @Override
    public boolean confirmPassword(ConfirmPasswordDto dto) {
        User user = userRepository.find(new Email(dto.getEmail()));
        if (user == null) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_USER_NOT_FOUND, dto.getEmail());
        }
        // 前端传递的密码为加密密码
        // 获取Aes秘钥
        // 从redis中获取临时秘钥
        String key = UserCenterConstant.USER_AES_PASSWORD + dto.getEmail();
        String aesPassword = RedisUtils.getCacheObject(key);
        String source = AesUtils.decrypt(dto.getPassword(), aesPassword);
        if (StringUtils.isEmpty(source)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_DECRYPT_OLD_PASSWORD_ERROR);
        }
        if (!user.validPassword(source, user.getPassword())) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_CHECK_PASSWORD_ERROR);
        }
        return true;
    }

    @Override
    public boolean editPassword(EditPasswordDto dto) {
        User user = userRepository.find(new Email(dto.getEmail()));
        if (user == null) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_USER_NOT_FOUND, dto.getEmail());
        }

        // 获取Aes秘钥
        // 从redis中获取临时秘钥
        String key = UserCenterConstant.USER_AES_PASSWORD + dto.getEmail();
        String aesPassword = RedisUtils.getCacheObject(key);
        if (StringUtils.isEmpty(aesPassword)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_OBTAIN_AES_KEY_ERROR, key);
        }
        // 解密旧密码
        String oldPassword = AesUtils.decrypt(dto.getOldPassword(), aesPassword);
        if (StringUtils.isEmpty(oldPassword)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_DECRYPT_OLD_PASSWORD_ERROR);
        }

        // 校验旧密码是否匹配
        if (!user.validPassword(oldPassword, user.getPassword())) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_OLD_PASSWORD_ERROR);
        }
        // 判断新密码和旧密码是否一致
        String newPassword = AesUtils.decrypt(dto.getPassword(), aesPassword);
        if (Objects.equals(newPassword, oldPassword)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_MODIFY_PASSWORD_ERROR);
        }
        user.chargePassword(newPassword, dto.getPassword(), aesPassword);
        userRepository.store(user);
        // 消息队列 更新SF用户密码
        streamProducer.streamSfUserMsg(SfUserMessage.TypeEnum.UPDATE, user);
        return true;
    }

    @Override
    public boolean resetPassword(ResetPasswordDto dto) {
        User user = userRepository.find(new Email(dto.getEmail()));
        if (user == null) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_USER_NOT_FOUND, dto.getEmail());
        }
        String key = dto.getEmail() + UserCenterConstant.REDIST_SPACER + UserCenterConstant.FORGET_PASSWORD;

        // 校验code
        // 校验邮箱验证码
        Object hashCode = RedisUtils.getCacheMapValue(key, UserCenterConstant.CODE);
        if (hashCode == null || !Objects.equals(dto.getCode(), hashCode)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_EMAIL_CODE_ERROR1);
        }
        // 获取Aes秘钥
        // 从redis中获取临时秘钥
        String keyAes = UserCenterConstant.USER_AES_PASSWORD + dto.getEmail();
        String aesPassword = RedisUtils.getCacheObject(keyAes);
        String sourcePassword = AesUtils.decrypt(dto.getPassword(), aesPassword);
        if (StringUtils.isEmpty(sourcePassword)) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_RESET_PASSWORD_ERROR);
        }
        user.chargePassword(sourcePassword, dto.getPassword(), aesPassword);
        user.setIsEmailValidated(CommonConstant.ONE);
        userRepository.store(user);
        // 消息队列 更新SF用户密码
        streamProducer.streamSfUserMsg(SfUserMessage.TypeEnum.UPDATE, user);
        return true;
    }

    @Override
    public boolean logOffUser(String email) {
        User user = userRepository.find(new Email(email));
        if (user == null) {
            throw ExceptionMessageUtil.getException(UserCenterErrorCode.USER_CENTER_USER_NOT_FOUND, email);
        }
        // 删除用户相关的缓存
        String registerKey = email + ":" + UserCenterConstant.REGISTER;
        RedisUtils.deleteObject(registerKey);
        String forgetPasswordCode = email + ":" + UserCenterConstant.FORGET_PASSWORD;
        RedisUtils.deleteObject(forgetPasswordCode);
        String aesPasswordCode = UserCenterConstant.USER_AES_PASSWORD + email;
        RedisUtils.deleteObject(aesPasswordCode);
        String registerKeyCount = UserCenterConstant.USER_EMAIL_CODE_COUNT + email + ":" + UserCenterConstant.REGISTER;
        RedisUtils.deleteObject(registerKeyCount);

        String forgetPasswordCount = UserCenterConstant.USER_EMAIL_CODE_COUNT + email + ":" + UserCenterConstant.FORGET_PASSWORD;
        RedisUtils.deleteObject(forgetPasswordCount);
        userRepository.delete(user.getUserId());
        // 消息队列 删除SF用户
        streamProducer.streamSfUserMsg(SfUserMessage.TypeEnum.DELETE, user);
        return true;
    }

    @Override
    public void reportPhoneInfo(Long userId, PhoneInfoDto phoneInfoDto) {
        User user = ConvertUtil.convert(phoneInfoDto, User.class);
        user.setUserId(userId);
        userRepository.updateUserPhone(user);
    }

    @Override
    @Async
    public void testRegisterUser(Integer start, Integer count) {
        // TODO 压测临时代码,待删除
        // 分批插入数据
        List<UserDo> addLists = new ArrayList<>();
        String uuid = "k3eKROY1w8Hf1Tv1";
        for (int i = start; i < start + count; i++) {
            UserDo userDo = new UserDo();
            userDo.setFirstName("randomUser" + i);
            userDo.setLastName("test" + i);
            // 真实密码:qwER12#$
            userDo.setPassword("9f736d100a7213cdd761c34924b49139");
            userDo.setEmail(userDo.getLastName() + userDo.getFirstName() + "@163.com");
            userDo.setStatus(CommonConstant.ONE);
            userDo.setGender(CommonConstant.ONE);
            userDo.setIsDeleted(CommonConstant.ZERO);
            String key = UserCenterConstant.USER_AES_PASSWORD + userDo.getEmail();
            RedisUtils.setCacheObject(key, uuid, Duration.ofDays(30));
            addLists.add(userDo);
        }
        userRepository.saveBatch(addLists);
    }

    @Override
    public void editUserInfo(UserEditDto userEditDto) {
        if (userEditDto.getId() == null) {
            return;
        }
        userRepository.update(new UserDo(), new LambdaUpdateWrapper<UserDo>().eq(UserDo::getId, userEditDto.getId())
                .set(UserDo::getFirstName, userEditDto.getFirstName())
                .set(UserDo::getLastName, userEditDto.getLastName()));
        // 消息队列 更新SF用户
        streamProducer.streamSfUserMsg(SfUserMessage.TypeEnum.UPDATE, UserConverter.userEditDtoToUser(userEditDto));
    }

    @Override
    public void editUserPhoto(UserEditDto userPhoto) {
        if (userPhoto.getId() == null) {
            return;
        }
        userRepository.update(new UserDo(), new LambdaUpdateWrapper<UserDo>().eq(UserDo::getId, userPhoto.getId())
                .set(UserDo::getPhoto, userPhoto.getPhoto()));
    }

    @Override
    public void editUserPresenceState(Long userId, String appPresenceCode) {
        if (userId != null && appPresenceCode != null && Arrays.asList("online", "offline").contains(appPresenceCode)) {
            UserDo user = new UserDo();
            user.setId(userId);
            user.setAppPresenceCode(appPresenceCode);
            userRepository.updateById(user);
        }
    }

    @Override
    public void syncUserFromSf(List<SfUserRecord> sfUserRecords) {
        if (CollectionUtils.isEmpty(sfUserRecords)) {
            return;
        }
        List<String> sfIds = sfUserRecords.stream().map(SfUserRecord::getSfUserId).collect(Collectors.toList());
        List<String> emails = sfUserRecords.stream().map(SfUserRecord::getUsername).collect(Collectors.toList());
        List<UserDo> userDos = userRepository.list(new LambdaQueryWrapper<UserDo>().in(UserDo::getSfUserId, sfIds).or().in(UserDo::getEmail, emails));
        Map<String, UserDo> userDoSfUserIdMap = userDos.stream().filter(r->StringUtils.isNotBlank(r.getSfUserId())).collect(Collectors.toMap(UserDo::getSfUserId,o->o,(v1,v2)->v1));
        Map<String, UserDo> userDoEmailMap = userDos.stream().collect(Collectors.toMap(r->r.getEmail().toLowerCase() , o->o,(v1, v2)->v1));
        List<UserDo> userDoAddList = new ArrayList<>();
        List<UserDo> userDoUpdateList = new ArrayList<>();
        for (SfUserRecord sfUserRecord : sfUserRecords) {
            try{
                UserDo userDo = UserConverter.sfUserRecordToUserDo(sfUserRecord);
                userDo.setLastSyncTime(LocalDateTime.now());
                // 对官网用户密码进行MD5加盐加密
                String salt = CommonUtil.getGUID(CommonConstant.EIGHT);
                String sourceStr = sfUserRecord.getPassword() + salt;
                String password = CommonUtil.encrypt3ToMD5(sourceStr);
                String sfUserId = sfUserRecord.getSfUserId();
                String email = sfUserRecord.getUsername().toLowerCase();
                if (userDoSfUserIdMap.get(sfUserId) == null && userDoEmailMap.get(email) == null) {
                    // 本地不存在该sfUserId用户, 新建
                    userDo.setSalt(salt);
                    userDo.setPassword(password);
                    // AES密码秘钥
                    String aesSalt = CommonUtil.getGUID(CommonConstant.SIXTEEN);
                    userDo.setAesSalt(aesSalt);
                    userDo.setAesPassword(AesUtils.encrypt(sfUserRecord.getPassword(), aesSalt));
                    // 从SF同步过来新建的用户需设置下面俩字段
                    userDo.setUserTypeCode("old");
                    userDo.setUserSourceCode("sf");
                    // 刚同步过来的SF用户邮箱验证标志位为否
                    userDo.setIsEmailValidated(CommonConstant.ZERO);
                    userDoAddList.add(userDo);
                } else if (userDoSfUserIdMap.get(sfUserId) == null && userDoEmailMap.get(email) != null) {
                    // 本地存在该email用户, 不存在sfUserId, 更新userDo中不为空的字段且包含sfUserId
                    UserDo userInIot = userDoEmailMap.get(email);
                    logoutWhenEditPassword(userInIot, sfUserRecord.getPassword());
                    userDo.setId(userInIot.getId());
                    userDo.setSalt(salt);
                    userDo.setPassword(password);
                    String aesSalt = userInIot.getAesSalt();
                    userDo.setAesSalt(aesSalt);
                    // AES密码(秘钥不变)
                    userDo.setAesPassword(AesUtils.encrypt(sfUserRecord.getPassword(), aesSalt));
                    userDoUpdateList.add(userDo);
                } else if (userDoSfUserIdMap.get(sfUserId) != null && userDoEmailMap.get(email) == null){
                    // 本地存在该sfUserId存在, email不存在, 更新userDo中不为空的字段且包含email
                    UserDo userInIot = userDoSfUserIdMap.get(sfUserId);
                    userDo.setId(userInIot.getId());
                    userDo.setSalt(salt);
                    userDo.setPassword(password);
                    // 设置IsEmailValidated=0，即邮箱需在app进行二次校验
                    userDo.setIsEmailValidated(CommonConstant.ZERO);
                    String aesSalt = userInIot.getAesSalt();
                    userDo.setAesSalt(aesSalt);
                    // AES密码(秘钥不变)
                    userDo.setAesPassword(AesUtils.encrypt(sfUserRecord.getPassword(), aesSalt));
                    userDoUpdateList.add(userDo);

                    //修改email退出登录
                    try {
                        StpUtil.logout(userInIot.getId());
                    } catch (Exception e) {
                        log.warn("userId:{}, logout error! ", userInIot.getId(), e);
                    }
                } else {
                    // 本地存在该sfUserId用户, email也存在, 更新userDo中不为空的字段
                    // 若 sfUserId 和 email 对应的 userId 不一样则丢弃该记录
                    if (!userDoSfUserIdMap.get(sfUserId).getId().equals(userDoEmailMap.get(email).getId())){
                        log.info("该SF用户ID: [{}] 和 email: [{}] 对应的用户信息在user表中存在多条记录", sfUserId, email);
                        continue;
                    }
                    UserDo userInIot = userDoSfUserIdMap.get(sfUserId);
                    logoutWhenEditPassword(userInIot, sfUserRecord.getPassword());
                    userDo.setId(userInIot.getId());
                    userDo.setSalt(salt);
                    userDo.setPassword(password);
                    String aesSalt = userInIot.getAesSalt();
                    userDo.setAesSalt(aesSalt);
                    // AES密码(秘钥不变)
                    userDo.setAesPassword(AesUtils.encrypt(sfUserRecord.getPassword(), aesSalt));
                    userDoUpdateList.add(userDo);
                }
            }catch (Exception e){
                log.warn("syncUser fail sfUserRecord :{} ", JsonUtils.toJson(sfUserRecord));
            }
        }
        log.info("UserCommandServiceImpl#syncUserFromSf -> synchronously add new users: {}", userDoAddList.size());
        log.info("UserCommandServiceImpl#syncUserFromSf -> synchronously update users: {}", userDoUpdateList.size());
        userRepository.saveBatch(userDoAddList);
        userRepository.updateBatchById(userDoUpdateList);
    }

    /**
     * SF用户修改密码后APP端退出登录
     * @param userInIot           IoT用户信息
     * @param passwordFromSf   SF平台用户密码
     */
    private void logoutWhenEditPassword(UserDo userInIot, String passwordFromSf) {
        try {
            String passwordInIot = AesUtils.decrypt(userInIot.getAesPassword(), userInIot.getAesSalt());
            if(!passwordInIot.equals(passwordFromSf)) {
                StpUtil.logout(userInIot.getId());
            }
        } catch (Exception e) {
            log.warn("userId:{}, logout error! ", userInIot.getId(), e);
        }
    }

}
