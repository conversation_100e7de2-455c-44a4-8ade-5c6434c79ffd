<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>user-center-parent</artifactId>
        <groupId>com.chervon</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>user-center-server</artifactId>
    <name>user-center-server</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <sk.version>8.15.0</sk.version>
        <aws-sdk-ses.version>1.12.215</aws-sdk-ses.version>
        <user-center.version>1.0.6-SNAPSHOT</user-center.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>user-center-api</artifactId>
            <version>${user-center.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- Chervon Common -->
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-i18n</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ldap</groupId>
            <artifactId>spring-ldap-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>configuration-center-sdk-language</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
        </dependency>

        <!-- Sa-Token 整合 Redis (使用jackson序列化方式) -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-dao-redis-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <!-- Forest 网络请求库：https://forest.dtflyx.com/ -->
        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>operation-platform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
            <version>5.8.2</version>
        </dependency>

        <!--skywalking traceId 记录到logback日志，请与安装的服务器版本对应-->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>${sk.version}</version>
        </dependency>

        <!-- SES -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-ses</artifactId>
            <version>${aws-sdk-ses.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>8.9.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
